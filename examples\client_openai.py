"""
OpenAI MCP Client with HTTP Transport

This client connects to MCP servers over HTTP transport and provides an interactive
chat interface where OpenAI's GPT models can access and use tools from the connected
MCP server. It mirrors the functionality of the Claude client but uses OpenAI's API.

Setup Instructions:
1. Install dependencies:
   pip install mcp openai python-dotenv
   OR
   bun install mcp openai python-dotenv (if using bun)

2. Set OpenAI API key:
   export OPENAI_API_KEY=your_key_here
   OR create a .env file with:
   OPENAI_API_KEY=your_key_here

3. Start the MCP server with HTTP transport:
   uv run uvicorn main:app --host 0.0.0.0 --port 8000

4. Run this client:
   python client_openai.py [server_url]

Features:
- HTTP transport with comprehensive error handling and timeout management
- URL validation and connection retry logic
- Interactive chat interface with OpenAI GPT-4o model
- Automatic tool discovery and usage with function calling
- Structured and unstructured tool result handling
- Persistent conversation history with context management
- Progress reporting and detailed logging support
- Async/await patterns for optimal performance
- Graceful error handling and recovery

API Compatibility:
- Uses OpenAI's latest chat completions API with function calling
- Supports both GPT-4o and GPT-3.5-turbo models
- Compatible with OpenAI's tool/function calling format
- Handles streaming and non-streaming responses

Example Usage:
    # Connect to local MCP server (default)
    python client_openai.py

    # Connect to custom local server
    python client_openai.py http://localhost:8000/mcp

    # Connect to remote server
    python client_openai.py https://your-server.com/mcp

    # Interactive commands:
    > Hello, what tools are available?
    > clear  # Clear conversation history
    > quit   # Exit the client

Architecture:
This client follows the same architectural patterns as the Claude client:
- MCPClient class encapsulates all functionality
- Streamable HTTP transport for MCP communication
- Async context managers for resource cleanup
- Conversation history management
- Tool discovery and execution pipeline
"""

from __future__ import annotations
import asyncio
import json
import sys
from typing import Optional, List, Any, Dict
from contextlib import AsyncExitStack
from urllib.parse import urlparse

from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client

from openai import AsyncOpenAI
from openai.types.chat import ChatCompletion
from mcp.types import TextContent

from dotenv import load_dotenv

load_dotenv()  # Load environment variables from .env file


class MCPClient:
    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.openai = AsyncOpenAI()
        self.conversation_history: List[Dict[str, Any]] = []  # Persistent conversation context

    async def connect(self, server_url: str = "http://localhost:8000/mcp"):
        """Connect to MCP Server over HTTP transport

        Args:
            server_url: HTTP URL of the MCP server (e.g., 'http://localhost:8000/mcp')
        """
        # Validate URL format
        try:
            parsed = urlparse(server_url)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError("Invalid URL format")
            if parsed.scheme not in ("http", "https"):
                raise ValueError("URL must use http or https scheme")
        except Exception as e:
            raise ValueError(f"Invalid server URL '{server_url}': {e}")

        try:
            # Connect using streamable HTTP transport with timeout
            print("Establishing HTTP transport connection...")
            http_transport = await asyncio.wait_for(
                self.exit_stack.enter_async_context(streamablehttp_client(server_url)),
                timeout=30.0,  # 30 second timeout for connection
            )
            self.read_stream, self.write_stream, self.session_id = http_transport

            # Create MCP session
            print("Creating MCP session...")
            self.session = await self.exit_stack.enter_async_context(ClientSession(self.read_stream, self.write_stream))

            # Initialize the session with timeout
            print("Initializing session...")
            await asyncio.wait_for(
                self.session.initialize(),
                timeout=10.0,  # 10 second timeout for initialization
            )

            # List available tools
            print("Discovering available tools...")
            toolsData = await self.session.list_tools()
            tools = toolsData.tools
            print(f"\n✅ Connected to MCP server at {server_url}")
            print(f"📋 Available tools: {[tool.name for tool in tools]}")

            if self.session_id:
                print(f"🔗 Session ID: {self.session_id}")

        except asyncio.TimeoutError:
            raise RuntimeError(f"Connection timeout: MCP server at {server_url} did not respond within 30 seconds")
        except ConnectionError as e:
            raise RuntimeError(f"Connection failed: Unable to reach MCP server at {server_url}: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to connect to MCP server at {server_url}: {e}")

    async def disconnect(self):
        """
        Disconnect from MCP server and clean up resources.

        Properly closes the HTTP transport connection and cleans up
        any associated resources.
        """
        try:
            if self.exit_stack:
                print("Disconnecting from MCP server...")
                await self.exit_stack.aclose()
                print("✅ Disconnected successfully")
        except Exception as e:
            print(f"⚠️  Warning during disconnect: {e}")

    def clear_conversation_history(self):
        """Clear the conversation history to start fresh."""
        self.conversation_history.clear()
        print("🔄 Conversation history cleared")

    async def query(self, query: str) -> str:
        """Process query using OpenAI and available tools"""
        # Add new user message to conversation history
        self.conversation_history.append({"role": "user", "content": query})
        messages: List[Dict[str, Any]] = self.conversation_history.copy()

        if not self.session:
            raise RuntimeError("Not connected to MCP server. Call connect() first.")

        toolData = await self.session.list_tools()
        available_tools: List[Dict[str, Any]] = []
        for tool in toolData.tools:
            tool_param: Dict[str, Any] = {
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description or "",
                    "parameters": tool.inputSchema,
                },
            }
            available_tools.append(tool_param)

        # Initialize OpenAI API call
        response: ChatCompletion = await self.openai.chat.completions.create(
            model="gpt-4o-mini",
            max_tokens=1000,
            messages=messages,  # type: ignore
            tools=available_tools if available_tools else None,  # type: ignore
        )

        final_text = []

        # Process the response content
        if response.choices and response.choices[0].message:
            message = response.choices[0].message
            assistant_content = {"role": "assistant", "content": message.content or ""}

            # Handle text content
            if message.content:
                final_text.append(message.content)

            # Handle tool calls
            if message.tool_calls:
                # Add tool calls to assistant content
                assistant_content["tool_calls"] = []  # type: ignore

                for tool_call in message.tool_calls:
                    if tool_call.function:
                        tool_name = tool_call.function.name
                        # Parse the function arguments
                        try:
                            tool_args: Dict[str, Any] = (
                                json.loads(tool_call.function.arguments) if tool_call.function.arguments else {}
                            )
                        except json.JSONDecodeError:
                            tool_args = {}

                        # Add tool call to assistant content
                        assistant_content["tool_calls"].append(
                            {
                                "id": tool_call.id,
                                "type": "function",
                                "function": {"name": tool_name, "arguments": tool_call.function.arguments},
                            }
                        )

                        result = await self.session.call_tool(tool_name, tool_args)
                        final_text.append(
                            f"[Calling tool {tool_name} with arguments {json.dumps(tool_args, indent=2)}]"
                        )

                # Add assistant message with tool calls
                messages.append(assistant_content)
                self.conversation_history.append(assistant_content)

                # Process tool call results
                if message.tool_calls:
                    for tool_call in message.tool_calls:
                        if tool_call.function:
                            tool_name = tool_call.function.name
                            try:
                                tool_args = (
                                    json.loads(tool_call.function.arguments) if tool_call.function.arguments else {}
                                )
                            except json.JSONDecodeError:
                                tool_args = {}

                            result = await self.session.call_tool(tool_name, tool_args)

                            # Convert MCP result content to string for OpenAI API
                            result_content = ""
                            if hasattr(result, "content") and result.content:
                                # Handle list of content blocks from MCP
                                if isinstance(result.content, list):
                                    for content_block in result.content:
                                        if isinstance(content_block, TextContent):
                                            result_content += content_block.text
                                        else:
                                            result_content += str(content_block)
                                else:
                                    result_content = str(result.content)
                            else:
                                result_content = str(result)

                            # Add tool result message
                            tool_result_message = {
                                "role": "tool",
                                "tool_call_id": tool_call.id,
                                "content": result_content,
                            }
                            messages.append(tool_result_message)
                            self.conversation_history.append(tool_result_message)

                    # Get OpenAI's response to the tool results
                    follow_up_response: ChatCompletion = await self.openai.chat.completions.create(
                        model="gpt-4o",
                        max_tokens=1000,
                        messages=messages,  # type: ignore
                        tools=available_tools if available_tools else None,  # type: ignore
                    )

                    # Add the follow-up response to final text and conversation history
                    if follow_up_response.choices and follow_up_response.choices[0].message:
                        follow_up_message = follow_up_response.choices[0].message
                        if follow_up_message.content:
                            final_text.append(follow_up_message.content)

                            # Add follow-up response to conversation history
                            follow_up_assistant_content = {"role": "assistant", "content": follow_up_message.content}
                            self.conversation_history.append(follow_up_assistant_content)
            else:
                # No tool calls, just add the assistant message
                self.conversation_history.append(assistant_content)

        return "\n".join(final_text)

    async def chat(self):
        """
        Interactive Chat Loop with enhanced error handling.

        Provides a user-friendly interface for chatting with OpenAI while
        handling various error conditions gracefully.
        """
        print("\nMCP Client Started!")
        print("Type your queries or 'quit' to exit.")
        print("Type 'clear' to reset conversation history.")
        print("OpenAI GPT has access to the MCP server tools and can use them to help you.\n")

        while True:
            try:
                query = input("> ").strip()
                if query.lower() in ("quit", "exit", "q"):
                    print("Alright then, Goodbye!")
                    break

                if query.lower() == "clear":
                    self.clear_conversation_history()
                    continue

                if not query:
                    continue

                print("🤔 OpenAI is thinking...")
                response = await self.query(query)
                print("\n" + "=" * 50)
                print("🤖 OpenAI's response:")
                print("=" * 50)
                print(response)
                print("=" * 50 + "\n")

            except KeyboardInterrupt:
                print("\n\nChat interrupted. Type 'quit' to exit gracefully.")
                break
            except ConnectionError as e:
                print(f"\n❌ Connection error: {e}")
                print("The MCP server may have disconnected. Please restart the client.")
                break
            except Exception as e:
                print(f"\n❌ Error: {str(e)}")
                print("Please try again or type 'quit' to exit.")


# Example usage
async def main():
    """
    Example usage of the MCPClient with OpenAI over HTTP transport.

    This client connects to an MCP server running over HTTP (such as the one
    started by running 'python main.py' in this repository) and provides
    an interactive chat interface where OpenAI GPT can use the available MCP tools.
    """
    # Default to local server if no URL provided
    if len(sys.argv) == 1:
        server_url = "http://localhost:8000/mcp"
        print(f"No server URL provided, using default: {server_url}")
    elif len(sys.argv) == 2:
        server_url = sys.argv[1]
    else:
        print("Usage: python client_openai.py [server_url]")
        print("Examples:")
        print("  python client_openai.py")
        print("  python client_openai.py http://localhost:8000/mcp")
        print("  python client_openai.py https://your-server.com/mcp")
        sys.exit(1)

    client = MCPClient()

    try:
        # Connect to your MCP server over HTTP
        print(f"Connecting to MCP server at {server_url}...")
        await client.connect(server_url=server_url)

        print("\n" + "=" * 50)
        print("🤖 OpenAI MCP Client with HTTP Transport")
        print("=" * 50)
        print("Connected successfully! You can now chat with OpenAI GPT.")
        print("OpenAI GPT has access to the MCP tools from the connected server.")
        print("Type 'quit' to exit.")
        print("=" * 50)

        await client.chat()

    except KeyboardInterrupt:
        print("\n\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure the MCP server is running")
        print("2. Verify the server URL is correct")
        print("3. Check that the server supports HTTP transport")
        print("4. Ensure your OPENAI_API_KEY environment variable is set")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
