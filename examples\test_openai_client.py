#!/usr/bin/env python3
"""
Test script for the OpenAI MCP Client

This script tests the basic functionality of the OpenAI MCP client without
requiring actual API keys or running servers.
"""

import sys
import os
import asyncio
from unittest.mock import Mock, patch, AsyncMock

# Add the examples directory to the path
sys.path.insert(0, os.path.dirname(__file__))

def test_imports():
    """Test that all required modules can be imported"""
    try:
        import client_openai
        print("✅ client_openai module imported successfully")
        
        # Check that the main class exists
        assert hasattr(client_openai, 'MCPClient'), "MCPClient class not found"
        print("✅ MCPClient class found")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_class_structure():
    """Test the basic structure of the MCPClient class"""
    try:
        import client_openai
        
        # Mock the OpenAI client to avoid API key requirement
        with patch('client_openai.AsyncOpenAI') as mock_openai:
            mock_openai.return_value = Mock()
            client = client_openai.MCPClient()
            
            # Check required methods exist
            required_methods = ['connect', 'disconnect', 'query', 'chat', 'clear_conversation_history']
            for method in required_methods:
                assert hasattr(client, method), f"Method {method} not found"
                print(f"✅ Method {method} found")
            
            # Check required attributes exist
            required_attrs = ['session', 'exit_stack', 'openai', 'conversation_history']
            for attr in required_attrs:
                assert hasattr(client, attr), f"Attribute {attr} not found"
                print(f"✅ Attribute {attr} found")
            
            print("✅ MCPClient class structure is correct")
            return True
            
    except Exception as e:
        print(f"❌ Class structure test failed: {e}")
        return False

async def test_basic_functionality():
    """Test basic functionality without external dependencies"""
    try:
        import client_openai
        
        # Mock all external dependencies
        with patch('client_openai.AsyncOpenAI') as mock_openai, \
             patch('client_openai.streamablehttp_client') as mock_http, \
             patch('client_openai.ClientSession') as mock_session:
            
            # Setup mocks
            mock_openai.return_value = AsyncMock()
            mock_http.return_value = AsyncMock()
            mock_session.return_value = AsyncMock()
            
            client = client_openai.MCPClient()
            
            # Test conversation history management
            assert len(client.conversation_history) == 0, "Initial conversation history should be empty"
            print("✅ Initial conversation history is empty")
            
            client.conversation_history.append({"role": "user", "content": "test"})
            assert len(client.conversation_history) == 1, "Conversation history should have one item"
            print("✅ Conversation history can be modified")
            
            client.clear_conversation_history()
            assert len(client.conversation_history) == 0, "Conversation history should be cleared"
            print("✅ Conversation history can be cleared")
            
            print("✅ Basic functionality tests passed")
            return True
            
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def test_documentation():
    """Test that the client has proper documentation"""
    try:
        import client_openai
        
        # Check module docstring
        assert client_openai.__doc__ is not None, "Module should have docstring"
        assert "OpenAI MCP Client" in client_openai.__doc__, "Module docstring should mention OpenAI MCP Client"
        print("✅ Module has proper docstring")
        
        # Check class docstring
        assert client_openai.MCPClient.__doc__ is None or "MCPClient" in str(client_openai.MCPClient.__doc__), "Class should have docstring"
        print("✅ Class documentation is present")
        
        # Check method docstrings
        methods_with_docs = ['connect', 'disconnect', 'query', 'chat']
        for method in methods_with_docs:
            method_obj = getattr(client_openai.MCPClient, method)
            assert method_obj.__doc__ is not None, f"Method {method} should have docstring"
            print(f"✅ Method {method} has docstring")
        
        print("✅ Documentation tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Documentation test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 Testing OpenAI MCP Client")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Class Structure Tests", test_class_structure),
        ("Basic Functionality Tests", test_basic_functionality),
        ("Documentation Tests", test_documentation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The OpenAI MCP client is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
